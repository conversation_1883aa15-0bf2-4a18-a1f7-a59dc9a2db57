import torch
import os
from datasets import load_dataset
from trl import DPOTrainer, DPOConfig, SFTTrainer, DataCollatorForCompletionOnlyLM,SFTConfig
from transformers import TrainingArguments, AutoModelForCausalLM, AutoTokenizer, TrainerCallback
import deepspeed
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from transformers import BitsAndBytesConfig

seed = 42
max_seq_length = 3072
learning_rate = 1e-4
per_device_train_batch_size = 2
gradient_accumulation_steps = 1
deepspeed_config = "./deepspeed_config/sft_lora_ds_config.json"

# 修改以下路径为你的实际路径
save_path = "./lora_params/"
model_path = "/opt/models/Seed-Coder-8B-Base"
tokenizer_path = "/opt/models/Seed-Coder-8B-Base"
dataset_flag = "mydata"   # "zeta" or "mydata"
num_train_epochs = 2    # 根据数据调整训练轮数

lora_config = LoraConfig(
    r=256,
    lora_alpha=256,
    target_modules=[
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ],
    lora_dropout=0.0,
    bias="none",
    task_type="CAUSAL_LM",
    inference_mode=False
)

# Load tokenizer first
tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)

# Add padding token if it doesn't exist
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Load model with proper device mapping for DeepSpeed
model = AutoModelForCausalLM.from_pretrained(
    model_path, 
    attn_implementation="flash_attention_2", 
    torch_dtype=torch.bfloat16,
    device_map=None,  # Let DeepSpeed handle device placement
    trust_remote_code=True
)

# Enable gradient checkpointing
model.gradient_checkpointing_enable()

# Prepare model for training
model = prepare_model_for_kbit_training(model)

# Apply LoRA
model = get_peft_model(model, lora_config)

# Ensure model is in training mode
model.train()

# Resize token embeddings if tokenizer has new tokens
if len(tokenizer) > model.config.vocab_size:
    model.resize_token_embeddings(len(tokenizer))

alpaca_prompt = """### Instruction:
You are a code completion assistant and your task is to analyze user edits and then rewrite an excerpt that the user provides, suggesting the appropriate edits within the excerpt, taking into account the cursor location.

### User Edits:

{}

### User Excerpt:

{}

### Response:

{}
"""

EOS_TOKEN = tokenizer.eos_token
original_start_marker = "<|editable_region_start|>"
original_end_marker = "<|editable_region_end|>"

def format_example(events, input, output):
    return alpaca_prompt.format(events, input, output)

def formatting_prompts_func(examples):
    events = examples["events"]
    inputs = examples["input"]
    outputs = examples["output"]
    texts = []
    for events, input, output in zip(events, inputs, outputs):
        output_start_index = output.find(original_start_marker)
        output_focused_region = output[output_start_index:]
        output_end_index = output_focused_region.find(original_end_marker)
        output = output_focused_region[:output_end_index + len(original_end_marker)]
        text = format_example(events, input, output) + EOS_TOKEN
        texts.append(text)
    return {"text": texts}

def filter_long_sequences(examples):
    tokenized = tokenizer(examples["text"])
    return len(tokenized['input_ids']) <= max_seq_length

if dataset_flag == "zeta":
    dataset = load_dataset("./datasets/zeta")
elif dataset_flag == "mydata":
    dataset = load_dataset("json", data_files={
        "train": "./data_synthesis/final_json_data/train.jsonl",
        "eval": "./data_synthesis/final_json_data/eval.jsonl"
    })
else:
    raise ValueError("Unsupported dataset flag. Use 'zeta' or 'mydata'.")

dataset = dataset.map(formatting_prompts_func, batched=True,)
train_dataset = dataset["train"].filter(filter_long_sequences)
eval_dataset = dataset["eval"].filter(filter_long_sequences)

response_template = "### Response:\n\n"
collator = DataCollatorForCompletionOnlyLM(response_template, tokenizer=tokenizer)

class CustomCallback(TrainerCallback):
    def on_log(self, args, state, control, logs=None, **kwargs):
        if logs is not None:
            print(f"训练损失: {logs.get('loss', 'N/A')}")

# Create training arguments with DeepSpeed configuration
training_args = SFTConfig(
    weight_decay=0.01,
    num_train_epochs=num_train_epochs,
    seed=seed,
    output_dir="./output_log",
    report_to="none",
    eval_strategy="epoch",
    do_eval=True,
    ddp_find_unused_parameters=False,
    bf16=True,
    fp16=False,
    deepspeed=deepspeed_config,
    gradient_checkpointing=True,
    max_seq_length=max_seq_length,
    per_device_train_batch_size=per_device_train_batch_size,
    gradient_accumulation_steps=gradient_accumulation_steps,
    learning_rate=learning_rate,
    dataloader_pin_memory=False,  # Important for DeepSpeed
    remove_unused_columns=False,
    # logging_steps=10, 
    # logging_dir="./logs",
)

trainer = SFTTrainer(
    model=model,
    processing_class=tokenizer,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    data_collator=collator,
    args=training_args,
    callbacks=[CustomCallback()]
)

# Train the model
trainer.train()

# Save the model
model.save_pretrained(save_path)
tokenizer.save_pretrained(save_path)