`data_synthesis`的目的是合成类`Zeta-finetune`数据集

# 问题
1. 数据样本可能太长，且修改的地方跨度很长(可编辑区域长)
2. 需要添加\<start\>和\<end\>标记
3. 只抽取若干步直觉上最先修改的

# 步骤
1. 过滤掉数据样本太长的---解决数据样本可能太长，减少prompt消耗
2. 过滤掉diff太长的---解决可编辑区域长
3. 要求模型写出中间状态的代码，并且仅有若干处修改。因此中间状态代码应该处于初始代码和最终代码中间
4. 评估模型：针对修改处的质量进行评分，并且针对要点进行打分，只要满分的合成数据可通过筛选
5. 计算初始代码和中间状态代码的diff，作为zeta数据集的event
6. 将初始代码、中间状态代码、最终状态代码进行标记添加，逻辑为取三者的最长前缀匹配和最长后缀匹配，且单独取一行作为mark标记

# 数据合成逻辑
1. `synthesis_local_batch.py`：批量用本地模型初步合成数据,在`temp_parquet`文件夹出现大量parquet文件，包含`old_content`、`new_content`、`intermediate_code`三个键
2. `utils_merge_parquet.py`:合并第一步生成的散装parquet文件
3. `eval_local_batch.py`: 增加`json_eval`、`flag`列，`flag`表示满分标签，`json_eval`表示评价结果
4. `utils_compute_diff.py`:增加`intermediate_diff`列
5. `utils_mark.py`:增加`mark_intermediate_code`,`mark_old_content`,`mark_new_content`列
6. `utils_split_train_eval_test.py`：按比例划分parquet为训练集、测试集和验证集，并且存为`jsonl`，同时过滤掉`intermediate_code==new_content`的内容，还需要`flag=True`

# 问题&可能的改进点
- 合成模型在合成数据集时，只有约50%的合成成功率(成功提取json)，可能提示词或者json提取函数需要优化
- 评分模型在合成数据集上进行评分时，温度为0的情况下评分仅有约$\frac{1}{3}$满分，后续可以降低温度进行多次评分，取综合评分，质量可能更高，或者换多种评分模型
- 即使评分模型通过后，仍然可能有中间代码和初始代码或者最终代码完全相同的，这个可以通过过滤实现
- mark标记如果只是取最长的前缀或者后缀的匹配，可能导致模型认为`start_mark`标记的下一行和`end_mark`标记的上一行必定含有修改。因此可以随机增加扰动
